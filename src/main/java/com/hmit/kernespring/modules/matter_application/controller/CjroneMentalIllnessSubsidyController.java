package com.hmit.kernespring.modules.matter_application.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.constants.CjroneConstants;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;
import com.hmit.kernespring.modules.cjrone.service.impl.CjroneStreetServiceImpl;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.hmit.kernespring.modules.matter_application.entity.CjroneMentalIllnessSubsidyEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneMentalIllnessSubsidyService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import com.hmit.kernespring.modules.sys.controller.AbstractController;

/**
 * 精神病住院补贴
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-06 14:22:35
 */
@RestController
@RequestMapping("matter_application/cjronementalillnesssubsidy")
public class CjroneMentalIllnessSubsidyController extends AbstractController {
    @Autowired
    private CjroneMentalIllnessSubsidyService cjroneMentalIllnessSubsidyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private CjroneStreetServiceImpl cjroneStreetService;

    /**
     * 列表
     */
    @RequestMapping("/list")
//    @RequiresPermissions("matter_application:cjronementalillnesssubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneMentalIllnessSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
//    @RequiresPermissions("matter_application:cjronementalillnesssubsidy:info")
    public R info(@PathVariable("id") Long id){
		CjroneMentalIllnessSubsidyEntity cjroneMentalIllnessSubsidy = cjroneMentalIllnessSubsidyService.getById(id);

        return R.ok().put("cjroneMentalIllnessSubsidy", cjroneMentalIllnessSubsidy);
    }

    /**
     * 根据身份证查询历史记录（按时间倒序）
     */
    @RequestMapping("/historyByIdCard")
//    @RequiresPermissions("matter_application:cjronementalillnesssubsidy:list")
    public R historyByIdCard(@RequestParam String idCard, @RequestParam(required = false) Integer status, @RequestParam(required = false) String subsidyYear) {
        if (StringUtils.isBlank(idCard)) {
            return R.ok();
        }
        List<CjroneMentalIllnessSubsidyEntity> historyList =
                cjroneMentalIllnessSubsidyService.queryHistoryByIdCard(idCard, status, subsidyYear);
        return R.ok().put("historyList", historyList);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
//    @RequiresPermissions("matter_application:cjronementalillnesssubsidy:save")
    public R save(@RequestBody CjroneMentalIllnessSubsidyEntity cjroneMentalIllnessSubsidy){
		cjroneMentalIllnessSubsidyService.save(cjroneMentalIllnessSubsidy);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
//    @RequiresPermissions("matter_application:cjronementalillnesssubsidy:update")
    public R update(@RequestBody CjroneMentalIllnessSubsidyEntity cjroneMentalIllnessSubsidy){
        CjroneMentalIllnessSubsidyEntity entity = cjroneMentalIllnessSubsidyService.getById(cjroneMentalIllnessSubsidy.getId());
        if (entity == null) return R.error("数据不存在");

        SysUserEntity user = getUser();
        if (user.getRoleName() != null && user.getRoleName().contains("区残联")) {
//            if(!Objects.equals(user.getZhen(),entity.getNativeZhen())){
//                return R.error(String.format("街道不匹配，当前用户街道是%s,审核的数据街道是%s",user.getZhen(),entity.getNativeZhen()));
//            }

            String frontendStatus = cjroneMentalIllnessSubsidy.getStatus();
            if ("8".equals(frontendStatus)) {
                cjroneMentalIllnessSubsidy.setStatus("8");
            } else {
                cjroneMentalIllnessSubsidy.setStatus("12");
            }
        } else return R.error("当前用户角色不是区残联，无法进行审核");

        boolean b = cjroneMentalIllnessSubsidyService.updateAudioById(cjroneMentalIllnessSubsidy);
        if (b) {
            return R.ok();
        }
        else{
            return R.error("该身份证号在" + cjroneMentalIllnessSubsidy.getSubsidyYear() +
                    "年度的累计报销金额已超过" + CjroneConstants.MENTAL_ILLNESS_SUBSIDY_YEAR_TOTAL_AMOUNT_LIMIT + "元限制");
        }
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
//    @RequiresPermissions("matter_application:cjronementalillnesssubsidy:delete")
    public R delete(@RequestBody Long[] ids){
		cjroneMentalIllnessSubsidyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
//    @RequiresPermissions("matter_application:cjronementalillnesssubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneMentalIllnessSubsidyEntity> cjroneMentalIllnessSubsidyList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get("自增主键ID"));
                    item.put("annualAccumulatedAmount",item.get("年度累计金额"));
                    item.put("isPsychiatric",item.get("是否精神科"));
                    item.put("hospitalName",item.get("医院名称"));
                    item.put("serialNumber",item.get("序号"));
                    item.put("applicationDate",item.get("申请日期"));
                    item.put("hospitalizationStartDate",item.get("住院时间（起）"));
                    item.put("hospitalizationEndDate",item.get("住院时间（止）"));
                    item.put("subsidyYear",item.get("补助年份"));
                    item.put("hospitalizationCount",item.get("住院次数"));
                    item.put("personalPayment",item.get("个人自付"));
                    item.put("classBPayment",item.get("乙类(先行)自付"));
                    item.put("insuranceOverageLimit",item.get("医保超限价"));
                    item.put("effectiveOopMedicalExpense",item.get("自付有效医疗费用"));
                    item.put("subsidyAmount",item.get("补助金额"));
                    item.put("applyRemarks",item.get("申请备注"));
                    item.put("createdAt",item.get("记录创建时间"));
                    item.put("updatedAt",item.get("记录更新时间"));
                    item.put("attach",item.get("附件"));
                    item.put("reviewRemarks",item.get("审批备注"));
                    item.put("welfareMatterApplicationId",item.get("惠残事项申请ID"));
                    item.put("operatorId",item.get("经办人ID"));
                    item.put("operatorName",item.get("经办人姓名"));
                    item.put("auditorId",item.get("审核人ID"));
                    item.put("auditorName",item.get("审核人姓名"));
                    item.put("status",item.get("状态"));
                    item.put("diagnosisCertificateImage",item.get("诊断证明图片URL"));
                    item.put("diagnosisCertificateStartTime",item.get("诊断证明时间-起始时间"));
                    item.put("diagnosisCertificateEndTime",item.get("诊断证明时间-结束时间"));
                    cjroneMentalIllnessSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), CjroneMentalIllnessSubsidyEntity.class));
        });
        // 保存到数据库
        cjroneMentalIllnessSubsidyService.saveBatch(cjroneMentalIllnessSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
//    @RequiresPermissions("matter_application:cjronementalillnesssubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneMentalIllnessSubsidyEntity> cjroneMentalIllnessSubsidyEntityList = cjroneMentalIllnessSubsidyService.queryExportData(mapArgs);

        cjroneMentalIllnessSubsidyEntityList.forEach(item ->{
            //开始处理性别
            if(item.getSex() != null){
                if("1".equals(item.getSex())){
                    item.setSexName("男");
                }else if("0".equals(item.getSex())){
                    item.setSexName("女");
                }
            }

            //处理Boolean字段，直接复制值，null值由@Excel注解的replace处理
            item.setIsPsychiatricDisplay(item.getIsPsychiatric());

            //处理街道编号到名称的映射
            if(item.getNativeZhen() != null && !item.getNativeZhen().isEmpty()) {
                if(item.getNativeZhen() != null && item.getNativeZhen().length() >= 6) {
                    try {
                        String areaCode = item.getNativeZhen().substring(0, 6);
                        List<CjroneAdministrativeDivisionEntity> streetList =
                                cjroneStreetService.getAdministrativeDivisionList(areaCode);
                        streetList.stream()
                                .filter(s -> s.getValue().equals(item.getNativeZhen()))
                                .findFirst()
                                .ifPresent(street -> item.setNativeZhen(street.getLabel()));
                    } catch (Exception e) {
                        System.out.println("街道名称查询失败，街道编码: " + item.getNativeZhen() + "，错误: " + e.getMessage());
                    }
                }
            }
        });

        ExportParams params = new ExportParams("精神病住院补贴", null, "精神病住院补贴");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneMentalIllnessSubsidyEntity.class, cjroneMentalIllnessSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "精神病住院补贴" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

    @GetMapping("/statistics")
    public R statistics(@RequestParam(required = false)String approvalYear){
      Map<String,Object> map= cjroneMentalIllnessSubsidyService.statistics(approvalYear);
      return R.ok().put("statistics",map);
    }
}
